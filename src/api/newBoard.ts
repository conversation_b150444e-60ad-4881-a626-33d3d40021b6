import request, { Res } from '@/utils/request'
import { getVisitorId, headerInfo } from '@/utils/tool'
import { GenericAbortSignal } from 'axios'
import md5 from 'md5'

// 获取绘画请求ID
export const getDrawId = () => {
  return request.get<Res, Res>(`/metaleap-api/v1/draw/anon/getDrawId`)
}

// ai图生图
export const img2img = async (data: any) => {
  const { prompt, height, imageUrl, styleId, width } = data
  const timestamp = new Date().getTime()
  const txt = `${prompt}${height}${imageUrl}${styleId}${width}`
  const txt2 = `${headerInfo.deviceId}${headerInfo.appVersion}${headerInfo.deviceType}${txt}${timestamp}${import.meta.env.VITE_APP_APPSECRET}`
  const sign = md5(txt2)
  const drawId = await getVisitorId
  return request.post<Res, Res>(`/metaleap-api/v1/screen/anon/img2img`, { ...data, timestamp, sign }, { headers: { drawId } })
}

// 考试提交
export const getClientTime = (data: any) => {
  return request.post<Res, Res>(`/metaleap-api/v1/screen/anon/client/time`, data)
}

// 获取分屏-画的结果
export const getDrawImg = (uniqueId: string) => {
  return request.get<Res, Res>(`/metaleap-api/v1/screen/anon/img?uniqueId=${uniqueId}`)
}

// 获取AI风格
export const aiStyles = () => {
  return request.get<Res, Res>(`/metaleap-api/v1/screen/anon/list/style`)
}



