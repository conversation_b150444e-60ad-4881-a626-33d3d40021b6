import { useNavigate, useSearchParams, useParams } from 'react-router-dom'
import { useState, useEffect } from 'react'
import { useAsyncFn } from 'react-use'
import { getDrawImg } from '@/api/newBoard'
import { QRCodeSVG } from 'qrcode.react'
import { BodyType, ipcInvoke, ipcOn, ipcRemoveListener, windowInfo } from '@/utils/electron'
export default function Index() {
  const { id: paramId } = useParams()
  const [generateImg, setGenerateImg] = useState<string>('') // 生成图片
  const { id: styleId } = JSON.parse(localStorage.themeStyle ?? '{}')

  useEffect(() => {
    const timer = setInterval(() => {
      worksDetailFetch()
    }, 1500)
    console.log('🚀 ~ useEffect ~ import:', import.meta.env)

    const handleMaximize = async (event: any, message: any) => {
      const isFull = await ipcInvoke('win', { sendWinName: 'display', method: 'isMaximized' })
      if (message.data?.winName === 'display' && isFull.data) {
        ipcInvoke('win', { sendWinName: 'display', method: 'setFullScreen' }, true)
      }
    }

    const handleClosed = (event: any, message: any) => {
      if (message.data?.winName === 'mainWin') {
        window.close()
      }
    }

    // 监听Esc键，退出全屏
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        ipcInvoke('win', { sendWinName: 'display', method: 'setFullScreen' }, false)
        ipcInvoke('win', { sendWinName: 'display', method: 'unmaximize' })
      }
    }
    ipcOn('maximize', handleMaximize)
    ipcOn('closed', handleClosed)

    if (!windowInfo.isMac) {
      window.addEventListener('keydown', handleKeyDown)
    }

    return () => {
      clearTimeout(timer)
      ipcRemoveListener('maximize', handleMaximize)
      ipcRemoveListener('closed', handleClosed)
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [])

  const [worksDetailState, worksDetailFetch] = useAsyncFn<() => Promise<any>>(async () => {
    const res = await getDrawImg(paramId as string)
    if (res.code !== 200) {
      return
    }
    setGenerateImg(res.data)
    return { ...res.data }
  }, [])

  const shareUrl = window.location.origin + '/shareCover?url=' + generateImg
  // console.log('🚀 ~ Index ~ shareUrl:', shareUrl)

  return (
    <div id="content" className={`play-theme-${styleId} w-screen h-screen  select-none  overflow-hidden touch-pan-x`}>
      {/* <div className="absolute top-0 left-0 z-[11] w-full h-[40px] drag"></div> */}
      <div className="frame">
        <div className="board w-[975px] h-[731px] bg-white overflow-hidden">{generateImg ? <img className="w-full h-full" src={generateImg} alt="" /> : null}</div>
      </div>

      <div className="absolute bottom-[186px] right-[64px] w-[300px] h-[386px] p-[45px] bg-[#F5F5F5] rounded-[23px] flex_center flex-col shadow-md">
        <div className=" mb-[20px]">神笔马良 AI</div>
        <div className="w-[188px] h-[188px] rounded-[10px] p-[8px] overflow-hidden bg-white mb-[10px]">
          {/* <QRCodeSVG value={shareUrl} style={{ width: '100%', height: '100%' }} /> */}
          <img src="https://wework.qpic.cn/wwpic3az/552867_aL9MUfefSVOv4vZ_1750659186/0" className="w-full h-full" alt="" />
        </div>
        <p className="text-[26px] text-[#5F5F5F]">扫码，免费领图</p>
      </div>
    </div>
  )
}
